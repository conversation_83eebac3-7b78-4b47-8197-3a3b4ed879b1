import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter_polyline_points/flutter_polyline_points.dart';
import 'package:gap/gap.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:storetrack_app/config/themes/app_colors.dart';
import 'package:storetrack_app/core/constants/app_assets.dart';
import 'package:storetrack_app/shared/widgets/custom_app_bar.dart';
import 'package:storetrack_app/core/services/location_service.dart';
import 'package:storetrack_app/core/storage/data_manager.dart';
import 'package:storetrack_app/core/utils/snackbar_service.dart';
import 'package:storetrack_app/di/service_locator.dart';
import 'package:url_launcher/url_launcher.dart';

@RoutePage()
class JourneyMapPage extends StatefulWidget {
  const JourneyMapPage({super.key});

  @override
  State<JourneyMapPage> createState() => _JourneyMapPageState();
}

class _JourneyMapPageState extends State<JourneyMapPage> {
  // Google Maps controller
  GoogleMapController? _mapController;

  // Default camera position (can be updated with user's location)
  static const CameraPosition _defaultPosition = CameraPosition(
    target: LatLng(-33.8688, 151.2093), // Sydney, Australia as default
    zoom: 14.0,
  );

  // Set of markers to display on the map
  final Set<Marker> _markers = {};

  // Set of polylines to display the journey path
  final Set<Polyline> _polylines = {};

  // List of LatLng points for the encoded polyline
  List<LatLng> _encodedPolylinePoints = [];

  // Controller for the draggable sheet
  final DraggableScrollableController _sheetController =
      DraggableScrollableController();

  // Current sheet size - always visible at 1/3 of the screen
  double _sheetSize = 0.33; // Fixed at 1/3 of screen

  // Flag to track if the map is loading
  bool _isMapLoading = true;

  // Encoded polyline string
  final String _encodedPolyline = "_p~iF~ps|U_ulLnnqC_mqNvxq`@";

  // We'll use default markers with custom colors since custom bitmap creation
  // requires UI rendering which is complex for this implementation

  @override
  void initState() {
    super.initState();

    // Initialize markers and polylines
    _initializeMapElements();

    // Ensure the bottom sheet is always visible
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (_sheetController.isAttached) {
        _sheetController.animateTo(
          0.33,
          duration: const Duration(milliseconds: 10),
          curve: Curves.easeInOut,
        );
      }
    });
  }

  // Initialize map elements (markers and polylines)
  void _initializeMapElements() {
    // Define the journey points
    const List<LatLng> journeyPoints = [
      LatLng(-33.8688, 151.2093), // Starting point (My location)
      LatLng(-33.8730, 151.2060), // First store (COLES)
      LatLng(-33.8650, 151.2150), // Second store (PETBARN)
      LatLng(-33.8710, 151.2080), // Third store (COLES again)
    ];

    // Decode the encoded polyline
    PolylinePoints polylinePoints = PolylinePoints();
    List<PointLatLng> decodedPoints =
        polylinePoints.decodePolyline(_encodedPolyline);

    // Convert decoded points to LatLng for Google Maps
    _encodedPolylinePoints = decodedPoints
        .map((point) => LatLng(point.latitude, point.longitude))
        .toList();

    // Add custom circle markers
    _addCircleMarker(
      id: 'myLocation',
      position: const LatLng(-33.8688, 151.2093),
      label: 'HOME',
      color: Colors.green,
    );

    _addCircleMarker(
      id: 'coles1',
      position: const LatLng(-33.8730, 151.2060),
      label: 'COLES',
      color: Colors.red,
    );

    _addCircleMarker(
      id: 'petbarn',
      position: const LatLng(-33.8650, 151.2150),
      label: 'PETBARN',
      color: Colors.orange,
    );

    _addCircleMarker(
      id: 'coles2',
      position: const LatLng(-33.8710, 151.2080),
      label: 'COLES',
      color: Colors.red,
    );

    // Add markers at key points of the encoded polyline
    if (_encodedPolylinePoints.isNotEmpty) {
      // Add marker at the start of the encoded polyline
      _addCircleMarker(
        id: 'encoded_start',
        position: _encodedPolylinePoints.first,
        label: 'START',
        color: Colors.blue,
      );

      // Add marker at approximately 1/3 of the polyline
      if (_encodedPolylinePoints.length > 2) {
        int oneThirdIndex = _encodedPolylinePoints.length ~/ 3;
        _addCircleMarker(
          id: 'encoded_1',
          position: _encodedPolylinePoints[oneThirdIndex],
          label: 'POINT 1',
          color: Colors.purple,
        );
      }

      // Add marker at approximately 2/3 of the polyline
      if (_encodedPolylinePoints.length > 3) {
        int twoThirdsIndex = (_encodedPolylinePoints.length * 2) ~/ 3;
        _addCircleMarker(
          id: 'encoded_2',
          position: _encodedPolylinePoints[twoThirdsIndex],
          label: 'POINT 2',
          color: Colors.cyan,
        );
      }

      // Add marker at the end of the encoded polyline
      _addCircleMarker(
        id: 'encoded_end',
        position: _encodedPolylinePoints.last,
        label: 'END',
        color: Colors.pink,
      );
    }

    // Create a polyline to connect all points (primary route) - solid white line
    _polylines.add(
      const Polyline(
        polylineId: PolylineId('journeyPath'),
        points: journeyPoints,
        color:
            Colors.white, // White color for better visibility on satellite view
        width: 4, // Slightly thinner for a cleaner look
        startCap: Cap.roundCap,
        endCap: Cap.roundCap,
        jointType: JointType.round,
      ),
    );

    // Create a polyline from the encoded string
    if (_encodedPolylinePoints.isNotEmpty) {
      _polylines.add(
        Polyline(
          polylineId: const PolylineId('encodedPath'),
          points: _encodedPolylinePoints,
          color: AppColors.primaryBlue, // Blue color for the encoded polyline
          width: 6, // Slightly thicker for emphasis
          startCap: Cap.roundCap,
          endCap: Cap.roundCap,
          jointType: JointType.round,
          patterns: [
            PatternItem.dash(20), // 20px dash
            PatternItem.gap(10), // 10px gap
          ],
        ),
      );

      // Update camera position to show the encoded polyline
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (_mapController != null && _encodedPolylinePoints.isNotEmpty) {
          // Calculate bounds for the polyline
          double minLat = _encodedPolylinePoints.first.latitude;
          double maxLat = _encodedPolylinePoints.first.latitude;
          double minLng = _encodedPolylinePoints.first.longitude;
          double maxLng = _encodedPolylinePoints.first.longitude;

          for (LatLng point in _encodedPolylinePoints) {
            if (point.latitude < minLat) minLat = point.latitude;
            if (point.latitude > maxLat) maxLat = point.latitude;
            if (point.longitude < minLng) minLng = point.longitude;
            if (point.longitude > maxLng) maxLng = point.longitude;
          }

          // Create bounds and add padding
          LatLngBounds bounds = LatLngBounds(
            southwest: LatLng(minLat, minLng),
            northeast: LatLng(maxLat, maxLng),
          );

          // Animate camera to show the entire polyline with padding
          _mapController!.animateCamera(
            CameraUpdate.newLatLngBounds(bounds, 50.0),
          );
        }
      });
    }
  }

  // Add a marker with custom color and label
  void _addCircleMarker({
    required String id,
    required LatLng position,
    required String label,
    required Color color,
  }) {
    // Convert color to BitmapDescriptor hue
    double hue = BitmapDescriptor.hueAzure;

    if (color == Colors.red) {
      hue = BitmapDescriptor.hueRed;
    } else if (color == Colors.green) {
      hue = BitmapDescriptor.hueGreen;
    } else if (color == Colors.blue) {
      hue = BitmapDescriptor.hueBlue;
    } else if (color == Colors.orange) {
      hue = BitmapDescriptor.hueOrange;
    } else if (color == Colors.yellow) {
      hue = BitmapDescriptor.hueYellow;
    } else if (color == Colors.purple) {
      hue = BitmapDescriptor.hueViolet;
    } else if (color == Colors.pink) {
      hue = BitmapDescriptor.hueMagenta;
    } else if (color == Colors.cyan) {
      hue = BitmapDescriptor.hueCyan;
    }

    _markers.add(
      Marker(
        markerId: MarkerId(id),
        position: position,
        // Use a flat marker icon which looks more like a circle
        icon: BitmapDescriptor.defaultMarkerWithHue(hue),
        // Set the label as the title in the info window
        infoWindow: InfoWindow(
          title: label,
          snippet: 'Tap for details',
        ),
        // Make the marker flat to look more like a circle on the map
        flat: true,
      ),
    );
  }

  PreferredSizeWidget _buildAppBar() {
    return CustomAppBar(
      title: 'Journey map',
      actions: [
        GestureDetector(
          child: Image.asset(
            AppAssets.appbarCalendar,
            scale: 4,
            color: AppColors.black,
          ),
          onTap: () {
            Navigator.pop(context);
          },
        ),
        const Gap(8),
        GestureDetector(
          child: Image.asset(
            AppAssets.appbarCalendarEdit,
            color: AppColors.black,
            scale: 4,
          ),
          onTap: () {
            Navigator.pop(context);
          },
        ),
        const Gap(8),
        GestureDetector(
          child: Image.asset(
            AppAssets.appbarMap,
            scale: 4,
            color: AppColors.primaryBlue,
          ),
          onTap: () {
              openRouteMap();

          },
        ),
        const Gap(8)
      ],
    );
  }

  // Open route map using StoreTrack web service
  Future<void> openRouteMap() async {
    try {
      // Get current location
      final locationService = sl<LocationService>();
      final currentLocation = await locationService.getCurrentPosition();

      if (!mounted) return;

      if (currentLocation == null) {
        SnackBarService.warning(
          context: context,
          message: 'Unable to get current location. Please enable location services.',
        );
        return;
      }

      // Get user ID
      final userID = await sl<DataManager>().getUserId() ?? "0";

      // Construct current location string
      final currentLatLng = "${currentLocation.latitude},${currentLocation.longitude}";

      // Construct the StoreTrack route URL
      final routeUrl = "https://webservice.storetrack.com.au/standalone/route.aspx?pid=$userID&latlong=$currentLatLng";

      // Launch the URL externally
      final uri = Uri.parse(routeUrl);
      if (await canLaunchUrl(uri)) {
        await launchUrl(uri, mode: LaunchMode.externalApplication);
      } else {
        if (mounted) {
          SnackBarService.error(
            context: context,
            message: 'Could not open route map. Please try again.',
          );
        }
      }
    } catch (e) {
      if (mounted) {
        SnackBarService.error(
          context: context,
          message: 'Error opening route map: ${e.toString()}',
        );
      }
    }
  }

  @override
  void dispose() {
    // Dispose of the controllers when the widget is disposed
    _mapController?.dispose();
    _sheetController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.lightGrey2,
      appBar: _buildAppBar(),
      body: Stack(
        children: [
          // Google Map takes the full screen
          GoogleMap(
            onMapCreated: (controller) {
              setState(() {
                _mapController = controller;
                _isMapLoading = false;
              });
            },
            initialCameraPosition: _defaultPosition,
            markers: _markers,
            polylines: _polylines,
            myLocationEnabled: true,
            myLocationButtonEnabled: true,
            mapType: MapType.normal,
            zoomControlsEnabled: false,
            compassEnabled: true,
          ),

          // Loading indicator overlay
          if (_isMapLoading)
            Container(
              color: Colors.white.withOpacity(0.7),
              child: const Center(
                child: CircularProgressIndicator(
                  color: AppColors.primaryBlue,
                ),
              ),
            ),

          // Bottom sheet that always overlaps the map
          NotificationListener<DraggableScrollableNotification>(
            onNotification: (notification) {
              setState(() {
                _sheetSize = notification.extent;
              });
              return true;
            },
            child: DraggableScrollableSheet(
              initialChildSize: 0.33, // Start at 1/3 of the screen
              minChildSize: 0.33, // Minimum size is fixed at 1/3 of the screen
              maxChildSize: 1.0, // Can expand to full available height
              snap: false, // Disable snapping to allow smooth dragging
              // No snap sizes to ensure smooth dragging from 1/3 to full
              controller: _sheetController,
              builder: (context, scrollController) {
                return Container(
                  decoration: BoxDecoration(
                    color: Colors.grey.shade100,
                    borderRadius: const BorderRadius.only(
                      topLeft: Radius.circular(16),
                      topRight: Radius.circular(16),
                    ),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withOpacity(0.1),
                        blurRadius: 10,
                        spreadRadius: 0,
                        offset: const Offset(0, -2),
                      ),
                    ],
                  ),
                  child: ListView(
                    controller: scrollController,
                    padding: const EdgeInsets.symmetric(horizontal: 16.0),
                    children: [
                      // Handle bar at top for dragging
                      GestureDetector(
                        onVerticalDragUpdate: (details) {
                          // This helps make the drag handle more responsive
                          if (scrollController.hasClients) {
                            scrollController.jumpTo(
                              scrollController.offset - details.delta.dy,
                            );
                          }
                        },
                        child: Center(
                          child: Container(
                            margin: const EdgeInsets.symmetric(vertical: 16),
                            width: 40,
                            height: 5,
                            decoration: BoxDecoration(
                              color: Colors.grey.shade400,
                              borderRadius: BorderRadius.circular(2.5),
                            ),
                          ),
                        ),
                      ),

                      // Journey timeline with vertical connecting line
                      Stack(
                        children: [
                          // Vertical connecting line
                          // Positioned(
                          //   left: 20,
                          //   top: 60,
                          //   bottom: 0,
                          //   width: 1,
                          //   child: Container(
                          //     color: Colors.grey.shade300,
                          //   ),
                          // ),

                          Column(
                            children: [
                              // My location card
                              _buildLocationCard(
                                icon: Icons.my_location,
                                iconColor: Colors.green,
                                title: 'My location',
                                subtitle: 'WOOLLAHRA',
                                showTime: false,
                              ),

                              // Dotted line connecting to next location
                              Padding(
                                padding: const EdgeInsets.only(left: 20.0),
                                child: DottedLine(
                                  direction: Axis.vertical,
                                  height: 150.0,
                                  width: 2.0,
                                  color: Colors.grey.shade400,
                                  dotRadius: 2.0,
                                  dotSpacing: 2.0,
                                ),
                              ),

                              // Distance and time info
                              _buildDistanceTimeInfo(
                                  distance: '1.5km', time: '5m'),

                              // Coles location card
                              _buildLocationCard(
                                icon: Icons.store,
                                iconColor: Colors.red,
                                title: 'COLES',
                                subtitle: 'BONDI JUNCTION',
                                time: '11.30am',
                                duration: '60m',
                              ),

                              // Distance and time info
                              _buildDistanceTimeInfo(
                                  distance: '1.2km', time: '4m'),

                              // Dotted line connecting to next location
                              Padding(
                                padding: const EdgeInsets.only(left: 20.0),
                                child: DottedLine(
                                  direction: Axis.vertical,
                                  height: 150.0,
                                  width: .50,
                                  color: Colors.grey.shade400,
                                  dotRadius: 2.0,
                                  dotSpacing: 4.0,
                                ),
                              ),

                              // Petbarn location card
                              _buildLocationCard(
                                icon: Icons.pets,
                                iconColor: Colors.amber,
                                title: 'PETBARN',
                                subtitle: 'PADDINGTON',
                                time: '12.15pm',
                                duration: '30m',
                              ),

                              // Distance and time info
                              _buildDistanceTimeInfo(
                                  distance: '1.7km', time: '7m'),

                              // Dotted line connecting to next location
                              Padding(
                                padding: const EdgeInsets.only(left: 20.0),
                                child: DottedLine(
                                  direction: Axis.vertical,
                                  height: 50.0,
                                  width: 2.0,
                                  color: Colors.grey.shade400,
                                  dotRadius: 2.0,
                                  dotSpacing: 4.0,
                                ),
                              ),

                              _buildLocationCard(
                                icon: Icons.store,
                                iconColor: Colors.red,
                                title: 'Home',
                                subtitle: 'BONDI JUNCTION',
                                time: '11.30am',
                                duration: '80m',
                              ),

                              // Distance and time info
                              _buildDistanceTimeInfo(
                                  distance: '1.5km', time: '14m'),
                            ],
                          ),
                        ],
                      ),
                    ],
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildLocationCard({
    required IconData icon,
    required Color iconColor,
    required String title,
    required String subtitle,
    String? time,
    String? duration,
    bool showTime = true,
  }) {
    return Card(
      margin: const EdgeInsets.symmetric(vertical: 8.0),
      elevation: 0,
      color: Colors.white,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Row(
          children: [
            // Icon circle
            Container(
              width: 40,
              height: 40,
              decoration: BoxDecoration(
                color: iconColor,
                shape: BoxShape.circle,
              ),
              child: Icon(
                icon,
                color: Colors.white,
                size: 20,
              ),
            ),
            const SizedBox(width: 16),

            // Location details
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Text(
                        title,
                        style:
                            Theme.of(context).textTheme.titleMedium?.copyWith(
                                  fontWeight: FontWeight.w600,
                                ),
                      ),
                      const Spacer(),
                      // Time information (if applicable)
                      if (showTime) ...[
                        Row(
                          children: [
                            Text(
                              time ?? '',
                              style: Theme.of(context).textTheme.titleSmall,
                            ),
                            const SizedBox(width: 8),
                            Icon(
                              Icons.access_time,
                              size: 16,
                              color: Colors.grey.shade600,
                            ),
                            const SizedBox(width: 4),
                            Text(
                              duration ?? '',
                              style: Theme.of(context)
                                  .textTheme
                                  .bodySmall
                                  ?.copyWith(
                                    color: Colors.grey.shade600,
                                  ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 4),
                      ],
                    ],
                  ),
                  const SizedBox(height: 4),
                  Row(
                    children: [
                      Icon(
                        Icons.location_on_outlined,
                        size: 16,
                        color: Colors.grey.shade600,
                      ),
                      const SizedBox(width: 4),
                      Text(
                        subtitle,
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                              color: Colors.grey.shade600,
                            ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDistanceTimeInfo({
    required String distance,
    required String time,
  }) {
    return Container(
      margin: const EdgeInsets.symmetric(vertical: 4.0),
      padding: const EdgeInsets.symmetric(vertical: 12.0, horizontal: 16.0),
      decoration: BoxDecoration(
        color: Colors.grey.shade200,
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        children: [
          // Distance
          Expanded(
            child: Row(
              children: [
                Icon(
                  Icons.directions_car_outlined,
                  size: 18,
                  color: Colors.grey.shade700,
                ),
                const SizedBox(width: 8),
                Text(
                  distance,
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        fontWeight: FontWeight.w500,
                      ),
                ),
              ],
            ),
          ),

          // Time
          Row(
            children: [
              Icon(
                Icons.access_time,
                size: 18,
                color: Colors.grey.shade700,
              ),
              const SizedBox(width: 8),
              Text(
                time,
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      fontWeight: FontWeight.w500,
                    ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}

// import 'package:flutter/material.dart';

class DottedLine extends StatelessWidget {
  final double height;
  final double width;
  final double dotSpacing;
  final double dotRadius;
  final Color color;
  final Axis direction;

  const DottedLine({
    super.key,
    this.height = 1.0,
    this.width = double.infinity,
    this.dotSpacing = 4.0,
    this.dotRadius = 2.0,
    this.color = Colors.black,
    this.direction = Axis.horizontal,
  });

  @override
  Widget build(BuildContext context) {
    return CustomPaint(
      size: direction == Axis.horizontal
          ? Size(width, height)
          : Size(height, width),
      painter: _DottedLinePainter(
        dotSpacing: dotSpacing,
        dotRadius: dotRadius,
        color: color,
        direction: direction,
      ),
    );
  }
}

class _DottedLinePainter extends CustomPainter {
  final double dotSpacing;
  final double dotRadius;
  final Color color;
  final Axis direction;

  _DottedLinePainter({
    required this.dotSpacing,
    required this.dotRadius,
    required this.color,
    required this.direction,
  });

  @override
  void paint(Canvas canvas, Size size) {
    // First draw the continuous line
    final linePaint = Paint()
      ..color = color
      ..style = PaintingStyle.stroke
      ..strokeWidth = dotRadius * 0.8; // Thinner than the dots

    // Draw the continuous line
    if (direction == Axis.horizontal) {
      final startPoint = Offset(0, size.height / 2);
      final endPoint = Offset(size.width, size.height / 2);
      canvas.drawLine(startPoint, endPoint, linePaint);
    } else {
      final startPoint = Offset(size.width / 2, 0);
      final endPoint = Offset(size.width / 2, size.height);
      canvas.drawLine(startPoint, endPoint, linePaint);
    }

    // Then draw the dots on top
    final dotPaint = Paint()
      ..color = color
      ..style = PaintingStyle.fill;

    double maxLength = direction == Axis.horizontal ? size.width : size.height;
    double currentPosition = 0;

    while (currentPosition < maxLength) {
      Offset offset = direction == Axis.horizontal
          ? Offset(currentPosition, size.height / 2)
          : Offset(size.width / 2, currentPosition);
      canvas.drawCircle(offset, dotRadius, dotPaint);
      currentPosition += dotSpacing + (dotRadius * 2);
    }
  }

  @override
  bool shouldRepaint(CustomPainter oldDelegate) => false;
}
